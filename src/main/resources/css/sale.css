/*
 * JavaFX CSS - Hoja de estilos específica para la funcionalidad de ventas
 * Este archivo contiene estilos para:
 * - SaleController (solo bienServicioCargados, se eliminó bienServicioDevueltos)
 * - BienServicioCargadoController
 * - bienServicioCargado.fxml
 * - sale.fxml
 */

/* Importar variables de tamaños de fuente */
@import "font-sizes.css";

/* Importar variables globales de colores desde styles.css */
@import "styles.css";

/* ===== Estilos para los ListViews en SaleController ===== */
.sale-list-view {
    -fx-background-color: -fx-control-inner-background;
    -fx-border-color: -fx-primary-color-light;
    -fx-effect: dropshadow(gaussian, rgba(0, 0, 0, 0.3), 5, 0.2, 0, 0);
    -fx-padding: 0;
}

/* Ocultar la barra de desplazamiento horizontal en los ListViews */
.sale-list-view .scroll-bar:horizontal {
    -fx-opacity: 0;
    -fx-pref-height: 0;
    -fx-min-height: 0;
    -fx-max-height: 0;
}

/* Estilos para las celdas de los ListViews */
.sale-list-view .list-cell {
    -fx-background-color: -fx-primary-color-dark;
    -fx-padding: 10px 2px;
    /* JavaFX no soporta margin directamente, usamos padding en el ListView */
    /* Eliminamos cualquier transición que pueda causar cambios de tamaño */
    -fx-transition: none;
    -fx-effect: none;
    /* Aseguramos que no haya cambios de tamaño */
    -fx-scale-x: 1.0;
    -fx-scale-y: 1.0;
    -fx-scale-z: 1.0;
}

/* Estilo para celdas vacías - hacerlas transparentes a eventos de mouse */
.sale-list-view .list-cell:empty {
    -fx-background-color: -fx-primary-color-dark;
    -fx-border-width: 0;
    -fx-opacity: 0;
    -fx-mouse-transparent: true;
    -fx-padding: 0;
}

/* Estilo para hover en celdas de ListView, solo cambia el color de fondo sin efectos de sombra */
.sale-list-view .list-cell:hover {
    -fx-background-color: derive(-fx-primary-color-dark, 25%);
    /* Eliminamos cualquier efecto o transformación que pueda causar cambios de tamaño */
    -fx-effect: none;
    -fx-transition: none;
    /* Aseguramos que no haya cambios de tamaño */
    -fx-scale-x: 1.0;
    -fx-scale-y: 1.0;
    -fx-scale-z: 1.0;
}

/* ===== Estilos para BienServicioCargado (Estilo Original Restaurado) ===== */
.bien-servicio-cargado {
    -fx-background-color: derive(-fx-primary-color-dark, 15%);
    -fx-padding: 5px 2px;
    -fx-border-radius: 8px;
    -fx-background-radius: 8px;
    -fx-border-color: derive(-fx-secondary-color, -20%);
    -fx-border-width: 1.5px;
    /* Agregamos un sutil efecto de sombra para dar apariencia de tarjeta */
    -fx-effect: dropshadow(gaussian, rgba(0, 0, 0, 0.4), 8, 0, 0, 2);
}

/* Estilo adicional para apariencia de tarjeta */
.card-style {
    -fx-background-color: derive(-fx-primary-color-dark, 15%);
    -fx-padding: 5px 2px;
    -fx-border-radius: 8px;
    -fx-background-radius: 8px;
    -fx-border-color: derive(-fx-secondary-color, -20%);
    -fx-border-width: 1.5px;
}

/* Estilos específicos para el ListView de bienes y servicios cargados */
#bienServicioCargados {
    /* Configuramos el ListView para que tenga espacio entre celdas */
    -fx-fixed-cell-size: 70.0;
    -fx-cell-horizontal-margin: 5.0;
    -fx-cell-vertical-margin: 10.0;
    -fx-padding: 5px;
    -fx-background-color: derive(-fx-primary-color-dark, -5%);
    -fx-transition: none;
}

/* Aseguramos que las celdas del ListView no tengan efectos de sombra */
#bienServicioCargados .list-cell {
    -fx-effect: none;
    -fx-transition: none;
    /* Aseguramos que no haya cambios de tamaño */
    -fx-scale-x: 1.0;
    -fx-scale-y: 1.0;
    -fx-scale-z: 1.0;
}

/* Estilos para celdas seleccionadas */
#bienServicioCargados .list-cell:selected {
    -fx-background-color: derive(-fx-secondary-color, 10%);
    -fx-effect: none;
    -fx-transition: none;
    /* Aseguramos que no haya cambios de tamaño */
    -fx-scale-x: 1.0;
    -fx-scale-y: 1.0;
    -fx-scale-z: 1.0;
    /* Aseguramos que los labels dentro de las celdas seleccionadas sean visibles */
    -fx-opacity: 1.0;
}

/* Estilos para celdas seleccionadas con hover - mantener el mismo color de selección */
#bienServicioCargados .list-cell:selected:hover {
    -fx-background-color: derive(-fx-secondary-color, 20%);
    /* Podemos agregar un sutil efecto de brillo para indicar el hover sin cambiar el color */
    -fx-effect: innershadow(gaussian, rgba(60, 160, 170, 0.5), 5, 0.3, 0, 0);
    -fx-transition: none;
    -fx-scale-x: 1.0;
    -fx-scale-y: 1.0;
    -fx-scale-z: 1.0;
    -fx-opacity: 1.0;
}

/* Estilos para celdas con hover (pero no seleccionadas) - Removemos :not() por compatibilidad */
#bienServicioCargados .list-cell:hover {
    -fx-background-color: derive(-fx-primary-color-dark, 30%);
    -fx-effect: none;
    -fx-transition: none;
    /* Aseguramos que no haya cambios de tamaño */
    -fx-scale-x: 1.0;
    -fx-scale-y: 1.0;
    -fx-scale-z: 1.0;
}

/* Asegurar que la descripción se ajuste correctamente */
.bien-servicio-descripcion {
    -fx-pref-width: 100%;
    -fx-wrap-text: true;
    -fx-text-overrun: ellipsis;
    -fx-font-weight: bold;
    -fx-text-fill: -fx-light-color;
    -fx-font-size: 14px;
    -fx-transition: none;
    -fx-effect: none;
}

/* Estilos para el código del item */
.bien-servicio-codigo {
    -fx-font-weight: bold;
    -fx-text-fill: -fx-secondary-color;
    -fx-font-size: 15px;
    -fx-transition: none;
    -fx-effect: none;
}

/* Estilo para la etiqueta de marca */
.marca-label {
    -fx-text-fill: -fx-light-color;
    -fx-font-weight: bold;
    -fx-font-size: 12px;
    -fx-transition: none;
    -fx-effect: none;
}

/* Asegurar que los labels dentro de celdas seleccionadas mantengan su visibilidad */
.list-cell:selected .bien-servicio-codigo,
.list-cell:selected .bien-servicio-descripcion,
.list-cell:selected .marca-label,
.list-cell:selected:hover .bien-servicio-codigo,
.list-cell:selected:hover .bien-servicio-descripcion,
.list-cell:selected:hover .marca-label {
    -fx-opacity: 1.0;
    -fx-effect: none;
    -fx-text-fill: -fx-light-color;
}

/* Mantener los colores originales para precio y cantidad cuando están seleccionados */
.list-cell:selected .bien-servicio-precio,
.list-cell:selected:hover .bien-servicio-precio {
    -fx-opacity: 1.0;
    -fx-effect: none;
    -fx-text-fill: -fx-info-color;
}

.list-cell:selected .bien-servicio-cantidad,
.list-cell:selected:hover .bien-servicio-cantidad {
    -fx-opacity: 1.0;
    -fx-effect: none;
    -fx-text-fill: -fx-light-color;
}

.list-cell:selected .bien-servicio-total,
.list-cell:selected:hover .bien-servicio-total {
    -fx-opacity: 1.0;
    -fx-effect: none;
    -fx-text-fill: -fx-success-color;
}

/* Estilos para el precio y cantidad (Estilo Original Restaurado) */
.bien-servicio-precio {
    -fx-font-weight: bold;
    -fx-text-fill: -fx-info-color;
    -fx-font-size: 14px;
    -fx-transition: none;
    -fx-effect: none;
}

.bien-servicio-cantidad {
    -fx-font-weight: bold;
    -fx-text-fill: -fx-light-color;
    -fx-font-size: 18px;
    -fx-transition: none;
    -fx-effect: none;
}

.bien-servicio-total {
    -fx-font-weight: bold;
    -fx-text-fill: -fx-success-color;
    -fx-font-size: 21px;
    -fx-transition: none;
    -fx-effect: none;
}

/* Estilos para items con stock cero o negativo */
.stock-error {
    -fx-background-color: -fx-error-color;
    /* Aseguramos que no haya efectos de sombra */
    -fx-effect: none;
}

.stock-error:hover {
    -fx-background-color: derive(-fx-error-color, 20%);
    /* Aseguramos que no haya efectos de sombra al hacer hover */
    -fx-effect: none;
}

/* ===== Estilos para CustomTextField que actúan como labels editables ===== */
.label-like {
    -fx-background-color: transparent;
    -fx-border-color: transparent;
    -fx-border-width: 0;
    -fx-padding: 0;
    -fx-cursor: default;
    -fx-focus-color: transparent;
    -fx-faint-focus-color: transparent;
    -fx-text-box-border: transparent;
    -fx-control-inner-background: transparent;
    -fx-background-radius: 0;
    -fx-border-radius: 0;
}

.label-like:focused {
    -fx-background-color: -fx-control-inner-background;
    -fx-border-color: -fx-accent;
    -fx-border-width: 1;
    -fx-padding: 2;
    -fx-cursor: text;
    -fx-background-radius: 3;
    -fx-border-radius: 3;
}

/* Evitar efectos de hover en CustomTextField para mantener apariencia de label */
.label-like:hover {
    -fx-background-color: transparent;
    -fx-border-color: transparent;
    -fx-effect: none;
}

/* ===== Estilos específicos para BienServicioCargado movidos a bienServicioCargado.css ===== */
/* Los estilos del contenido expandido y campos editables ahora están en su propio archivo CSS */

/* ===== Estilos para Diálogos de Venta ===== */
/* Estilos para títulos de diálogos */
.dialog-title {
    -fx-text-fill: #dbdce1;
    -fx-font-size: 13px;
    -fx-font-weight: 600;
}

.dialog-title-large {
    -fx-text-fill: #dbdce1;
    -fx-font-size: 14px;
    -fx-font-weight: 600;
}

/* Estilos para etiquetas de código y descripción */
.dialog-code-label {
    -fx-text-fill: #3ca0aa;
    -fx-font-size: 11px;
    -fx-font-weight: bold;
    -fx-min-width: 80px;
}

.dialog-code-value {
    -fx-text-fill: #dbdce1;
    -fx-font-size: 11px;
}

.dialog-desc-label {
    -fx-text-fill: #3ca0aa;
    -fx-font-size: 11px;
    -fx-font-weight: bold;
    -fx-min-width: 80px;
}

.dialog-desc-value {
    -fx-text-fill: #dbdce1;
    -fx-font-size: 11px;
}

/* Estilos para etiquetas de descripción y navegación */
.dialog-description {
    -fx-text-fill: #a0a0a0;
    -fx-font-size: 12px;
}

.dialog-format-hint {
    -fx-text-fill: #9ca3af;
    -fx-font-size: 10px;
    -fx-font-style: italic;
}

.dialog-navigation-hint {
    -fx-text-fill: #9ca3af;
    -fx-font-size: 10px;
    -fx-font-style: italic;
}

/* Estilos para ListView de selección de items */
.item-selection-list {
    -fx-background-color: #3a3a3a;
    -fx-border-color: #115f68;
    -fx-border-width: 1px;
    -fx-border-radius: 3px;
    -fx-background-radius: 3px;
}

/* Estilos para celdas del ListView de selección */
.item-selection-cell {
    -fx-font-size: 12px;
    -fx-padding: 8px 12px;
}

.item-selection-cell-normal {
    -fx-background-color: transparent;
    -fx-text-fill: #dbdce1;
}

.item-selection-cell-selected {
    -fx-background-color: #333e7e;
    -fx-text-fill: #dbdce1;
}

.item-selection-cell-hover {
    -fx-background-color: derive(#3ca0aa, 20%);
    -fx-text-fill: #dbdce1;
}

/* Estilos para items con stock bajo */
.item-selection-cell-low-stock {
    -fx-background-color: transparent;
    -fx-text-fill: #e74c3c;
    -fx-font-weight: bold;
}

.item-selection-cell-low-stock-selected {
    -fx-background-color: derive(#e74c3c, -20%);
    -fx-text-fill: #ffffff;
    -fx-font-weight: bold;
}

.item-selection-cell-low-stock-hover {
    -fx-background-color: derive(#e74c3c, 20%);
    -fx-text-fill: #ffffff;
    -fx-font-weight: bold;
}

/* ===== Estilos mejorados para el diálogo de selección de items (JavaFX 21) ===== */

/* SOLUCIÓN: Anular la limitación de max-width para el diálogo de selección de items */
.dialog-pane.item-selection-dialog {
    -fx-max-width: none; /* Anular la limitación de 380px del styles.css */
    -fx-pref-width: 950px; /* Asegurar el ancho preferido */
    -fx-min-width: 950px; /* Asegurar el ancho mínimo */
}

/* Estilos para el TableView del diálogo de selección */
.dialog-pane .items-table-view {
    -fx-background-color: -fx-primary-color-light;
    -fx-border-color: -fx-secondary-color;
    -fx-border-width: 1px;
    -fx-border-radius: 5px;
    -fx-background-radius: 5px;
    -fx-table-cell-border-color: derive(-fx-primary-color-dark, 20%);
    -fx-effect: dropshadow(gaussian, rgba(0, 0, 0, 0.2), 5, 0.3, 0, 2);
}

/* Cabeceras de columnas del diálogo */
.dialog-pane .items-table-view .column-header-background {
    -fx-background-color: -fx-secondary-color-dark;
}

.dialog-pane .items-table-view .column-header {
    -fx-background-color: -fx-secondary-color-dark;
    -fx-text-fill: -fx-light-color;
    -fx-font-weight: bold;
    -fx-font-size: 12px;
    -fx-border-color: derive(-fx-secondary-color-dark, 30%);
    -fx-border-width: 0 1px 1px 0;
    -fx-alignment: center;
}

.dialog-pane .items-table-view .column-header .label {
    -fx-text-fill: -fx-light-color;
    -fx-font-weight: bold;
    -fx-font-size: 12px;
}

/* Filas del TableView del diálogo */
.dialog-pane .items-table-view .table-row-cell {
    -fx-background-color: derive(-fx-primary-color-light, 5%);
    -fx-text-fill: -fx-light-color;
    -fx-border-color: transparent;
    -fx-border-width: 0;
    -fx-padding: 2px;
}

.dialog-pane .items-table-view .table-row-cell:hover {
    -fx-background-color: derive(-fx-secondary-color, 40%);
    -fx-transition: -fx-hover-transition;
}

.dialog-pane .items-table-view .table-row-cell:selected {
    -fx-background-color: -fx-secondary-color;
    -fx-text-fill: -fx-light-color;
}

/* Celdas del TableView del diálogo */
.dialog-pane .items-table-view .table-cell {
    -fx-alignment: center;
    -fx-padding: 4px 8px;
    -fx-border-color: derive(-fx-primary-color-dark, 30%);
    -fx-border-width: 0 1px 0 0;
    -fx-font-size: 11px;
    -fx-font-weight: 500;
    -fx-text-fill: -fx-light-color;
}

/* Estilos específicos para filas con stock cero o negativo en el diálogo */
.dialog-pane .items-table-view .table-row-cell.zero-stock-row {
    -fx-background-color: derive(-fx-error-color, 10%);
    -fx-text-fill: -fx-light-color;
}

.dialog-pane .items-table-view .table-row-cell.zero-stock-row:hover {
    -fx-background-color: derive(-fx-error-color, 25%);
}

.dialog-pane .items-table-view .table-row-cell.zero-stock-row:selected {
    -fx-background-color: derive(-fx-error-color, -10%);
    -fx-text-fill: -fx-light-color;
}

/* Eliminar bordes de foco del TableView del diálogo */
.dialog-pane .items-table-view:focused {
    -fx-background-color: transparent;
    -fx-background-insets: 0;
    -fx-background-radius: 5px;
    -fx-border-color: -fx-secondary-color;
    -fx-border-width: 2px;
    -fx-border-radius: 5px;
}

/* Ocultar scrollbars horizontales en el diálogo */
.dialog-pane .items-table-view .scroll-bar:horizontal {
    -fx-opacity: 0;
    -fx-pref-height: 0;
    -fx-min-height: 0;
    -fx-max-height: 0;
}


